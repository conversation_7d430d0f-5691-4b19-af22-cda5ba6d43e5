<?php

namespace App\Command;

use App\Enum\SocketCommunication;
use App\Message\LoggerMessage;
use App\Service\DecryptionService;
use App\Service\DeviceConfigurationService;
use App\Service\EncodeSettingsService;
use App\Service\FirmwareService;
use App\Service\LoyaltyService;
use App\Service\MessageService;
use App\Service\SocketManagementService;
use DateTime;
use Psr\Log\LoggerInterface;
use React\EventLoop\Loop;
use React\Socket\ConnectionInterface;
use React\Socket\TcpServer;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Style\SymfonyStyle;
use Symfony\Component\Messenger\MessageBusInterface;
use Doctrine\DBAL\Connection;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;

#[AsCommand(name: 'app:tcp-server')]
class StartTCPServerCommand extends Command
{
    protected static $defaultName = 'app:tcp-server';
    private const PORT = 4000;
    private const CONFIGURATION_CACHE_TIME = 'PT1H';

    public function __construct(
    private DecryptionService $decryptionService,
    private DeviceConfigurationService $deviceConfigService,
    private MessageBusInterface $bus,
    private MessageService $messageService,
    private LoyaltyService $loyaltyService,
    private EncodeSettingsService $ess,
    private SocketManagementService $socketManager,
    private FirmwareService $firmwareService,
    private LoggerInterface $logger,
    private Connection $doctrineConnection,
    private ParameterBagInterface $params,
    )
    {
        parent::__construct();
    }

    protected function configure(): void
    {
        $this->setDescription('Starts the TCP server');
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $io = new SymfonyStyle($input, $output);

        $this->firmwareService->loadFirmware();

        // Redeploy

        $loop = Loop::get();
        $server = new TcpServer('0.0.0.0:' . self::PORT, $loop);

        $this->logger->warning('TCP Server started on port ' . self::PORT);

        $server->on('connection', fn(ConnectionInterface $connection) => $this->handleNewConnection($connection, $io));

        $loop->addPeriodicTimer(35, function () {
            $this->sendKeepalivePackets();
        });

        $loop->run();
        return Command::SUCCESS;
    }

    private function handleNewConnection(ConnectionInterface $connection, SymfonyStyle $io): void
    {
        $salt = random_bytes(16);
        $connection->write(bin2hex($salt));

        $data = '';
        $imei = '';

        $connection->on('data', function ($chunk) use ($connection, &$data, $salt, $io, &$imei) {
            $data .= $chunk;

            $requestUUID = $this->generateUUID();
            $this->logger->warning(json_encode(["data" => $data, "chunk" => $chunk, "UUID" => $requestUUID]));

            if (str_ends_with($data, "\n")) {

                try{
                    $this->doctrineConnection->executeQuery("SELECT 1");
                }catch(\Exception $e){
                    $this->doctrineConnection->close();
                    $this->doctrineConnection->connect();
                }

                $serverMessage = json_decode($data);

                if(isset($serverMessage->type)){
                    $this->handleServerMessages($serverMessage, $connection);
                    return;
                }

                $separatedData = explode("\n", $data);

                if(count($separatedData) > 2) {
                    $this->logger->warning("More than one message received, ending request.");
                    return;
                };

                $data = trim($data);
                
                $this->logger->warning(json_encode(["dataBeforeProcessing" => $data, "UUID" => $requestUUID]));
                $imei = $this->processLoggerData($salt, $data, $io, $connection, $requestUUID);
                $data = ''; // Reset buffer

                if($imei){
                    $this->socketManager->addSocket($imei, $connection);

                    if(!$this->socketManager->getConnectedLogger($imei)){
                        $this->socketManager->addConnectedLogger($imei, ["version" => "", "lastPing" => ""]);
                    }
                }
            }else{
                $this->logger->warning(json_encode(["error" => "Data does not end with a new line", "data" => $data]));
            }
        });

        $connection->on("end", function() use(&$imei) {
            if($imei){
                $this->socketManager->removeSocket($imei);
                $this->socketManager->removeConnectedLogger($imei);
                $this->socketManager->removeSocketLastMessage($imei);
            }
        });

        $connection->on("close", function() use(&$imei) {
            if($imei){
                $this->socketManager->removeSocket($imei);
                $this->socketManager->removeConnectedLogger($imei);
                $this->socketManager->removeSocketLastMessage($imei);
            }
        });

        $connection->on("error", function(\Exception $e) use($connection, &$imei) {
            if($imei){
                $this->socketManager->removeSocket($imei);
                $this->socketManager->removeConnectedLogger($imei);
                $this->socketManager->removeSocketLastMessage($imei);
            }

            $connection->close();

            $this->logger->warning(json_encode(["error" => "Connection error: " . $e->getMessage(), "imei" => $imei ?? null]));
        });

        $connection->on('drain', function () use ($connection): void {
            $this->logger->warning(json_encode(["drainEvent" => "Stream is now ready to accept more data"]));
        });
    }

    private function processLoggerData(string $salt, string $data, SymfonyStyle $io, ConnectionInterface $connection, string $requestUUID): string | null
    {
        if (in_array($data[0], ['U', 'u'])) {
            try{
                $this->firmwareService->firmwareUpdate($data, $connection);
            }catch(\Exception $e){
                $this->logger->warning(json_encode(["error" => "Something went wrong with firmware update: " . $e->getMessage()]));
                $connection->write("\n");
            }
            return null;
        }

        if (strlen($data) < 17) {
            $this->logger->warning(json_encode(["error" => 'Data too short', "data" => $data]));
            $connection->close();
            return null;
        }

        $decodedData = $this->decodeData($data, $salt);
        

        if ($decodedData === null) {
            $this->logger->warning(json_encode(["error" => 'Failed to decode data', "data" => $data]));
            $connection->close();
            return null;
        }

        $imei = substr($decodedData, 1, 15);

        if (!$this->isValidData($decodedData, $imei)) {
            $this->logger->warning(json_encode(["error" => 'Invalid data', "data" => $decodedData, "imei" => $imei]));
            $connection->close();
            return null;
        }

        

        $cachedConfig = $this->deviceConfigService->getCachedConfiguration($imei);
        $config = null;

        $shouldRefreshConfig = true;

        if ($cachedConfig) {
            $cacheTimestamp = new \DateTime($cachedConfig["timestamp"]);
            $expirationTime = (clone $cacheTimestamp)->add(new \DateInterval(self::CONFIGURATION_CACHE_TIME));

            if ((new \DateTime()) < $expirationTime) {
                $config = $cachedConfig["configuration"];
                $shouldRefreshConfig = false;
            }
        }

        if ($shouldRefreshConfig) {
            $config = $this->deviceConfigService->getByImei($imei);
            $this->deviceConfigService->addCachedConfiguration($imei, $config);
        }

        if(!$config){
            $this->logger->warning(json_encode(["error" => 'Failed to retrieve the configuration.', "imei" => $imei]));
            $connection->close();
            return null;
        }

        $decodedConfig = json_decode($config["configuration"], true);

        $this->logger->warning(json_encode(["decodedData" => $decodedData, "imei" => $imei, "UUID" => $requestUUID]));

        $messages = $this->extractMessages(substr($decodedData, 17), $decodedConfig);


        $this->logger->warning(json_encode(["messages" => json_encode($messages), "imei" => $imei, "UUID" => $requestUUID]));

        $startUpdate = false;
        $loggerToUpdate = $this->socketManager->getLoggerToUpdate($imei);

        if($loggerToUpdate){
            $startUpdate = true;
        }

        $requestFeedback = false;
        $rfcardId = null;

        foreach($messages as $message){
            if(isset($message["type"])){
                switch($message["type"]){
                    case 'feedback':
                        $requestFeedback = true;
                        break;
                    case 'pay_rfcard':
                        $rfcardId = $message["card_id"];
                        break;
                    case 'ping':
                        $firmware = $this->firmwareService->getData();
                        $this->socketManager->addConnectedLogger($imei, ["version" => $message["version"] ?? null, "lastPing" => date('Y-m-d H:i:s')]);

                        if($loggerToUpdate and isset($message["version"]) and isset($firmware["kintetisVersion"]) and $firmware["kintetisVersion"] == $message["version"]){
                            $this->socketManager->removeLoggerToUpdate($imei);
                        }

                        if($loggerToUpdate and isset($message["version"]) and isset($firmware["tivaVersion"]) and $firmware["tivaVersion"] == $message["version"]){
                            $this->socketManager->removeLoggerToUpdate($imei);
                        }
                        break;
                    default:
                        break;
                }
            }
        }

        $dateTime = new DateTime();
        $currentHour = $dateTime->format('G');

        if($currentHour == 3){
            $this->socketManager->removeAllRestartedLoggers();
            $this->socketManager->clearLoggerMessageTimestamps();
        }


        $saldoOk = false;
        if($rfcardId){
            $card = $this->loyaltyService->getCard($rfcardId);

            if($card and $rfcardId == $card["serialNumber"] and $card['saldo'] > 0 and $card["isActive"]){
                $saldoOk = true;
                if($requestFeedback){
                    $response = "02" . $this->ess->encodeSettingsElement("", "f", "byte", count($messages)) . $this->ess->encodeSettingsElement("", "R", "byte", 1) . ">>>";
                    // $writeResponse = $connection->write($response);
                    $writeResponse = $this->socketSeparatedWrite($imei, $response, $connection);
                    $this->logger->warning(json_encode(["writeResponse" => $writeResponse, "imei" => $imei, "UUID" => $requestUUID]));
                    $this->logger->warning(json_encode(["response" => $response, "imei" => $imei, "UUID" => $requestUUID]));

                }else{
                    $response = $this->ess->encodeSettingsElement("01", "R", "byte", 1) . ">>>";
                    // $writeResponse = $connection->write($this->ess->encodeSettingsElement("01", "R", "byte", 1) . ">>>");
                    $writeResponse = $this->socketSeparatedWrite($imei, $response, $connection);
                    $this->logger->warning(json_encode(["writeResponse" => $writeResponse, "imei" => $imei, "UUID" => $requestUUID]));
                    $this->logger->warning(json_encode(["response" => $response, "imei" => $imei, "UUID" => $requestUUID]));
                }
            }else{
                if($requestFeedback){
                    $response = "02" . $this->ess->encodeSettingsElement("", "f", "byte", count($messages)) . $this->ess->encodeSettingsElement("", "R", "byte", 0) . ">>>";
                    // $writeResponse = $connection->write($response);
                    $writeResponse = $this->socketSeparatedWrite($imei, $response, $connection);
                    $this->logger->warning(json_encode(["writeResponse" => $writeResponse, "imei" => $imei, "UUID" => $requestUUID]));
                    $this->logger->warning(json_encode(["response" => $response, "imei" => $imei, "UUID" => $requestUUID]));
                }else{
                    $response = $this->ess->encodeSettingsElement("01", "R", "byte", 0) . ">>>";
                    // $writeResponse = $connection->write($this->ess->encodeSettingsElement("01", "R", "byte", 0) . ">>>");
                    $writeResponse = $this->socketSeparatedWrite($imei, $response, $connection);
                    $this->logger->warning(json_encode(["writeResponse" => $writeResponse, "imei" => $imei, "UUID" => $requestUUID]));
                    $this->logger->warning(json_encode(["response" => $response, "imei" => $imei, "UUID" => $requestUUID]));
                }
            }
        }
        else if(!$startUpdate and $currentHour == 2 and !$this->socketManager->getRestartedLogger($imei)){
            $this->logger->warning(json_encode(["message" => "Start reset", "imei" => $imei]));
            $response = $this->ess->encodeSettingsElement("01", 'P', 'byte', 0) . ">>>";

            // $writeResponse = $connection->write($this->ess->encodeSettingsElement("01", 'P', 'byte', 0) . ">>>");
            $writeResponse = $this->socketSeparatedWrite($imei, $response, $connection);
            $this->logger->warning(json_encode(["writeResponse" => $writeResponse, "imei" => $imei, "UUID" => $requestUUID]));
            $this->logger->warning(json_encode(["response" => $response, "imei" => $imei, "UUID" => $requestUUID]));

            $this->socketManager->addRestartedLogger($imei, true);
        }else if($decodedConfig){
            $response = $this->deviceConfigService->encodeSettings($decodedConfig, $startUpdate, $imei, $requestFeedback, count($messages)) . ">>>";
            // $writeResponse = $connection->write($response);
            $writeResponse = $this->socketSeparatedWrite($imei, $response, $connection);
            $this->logger->warning(json_encode(["writeResponse" => $writeResponse, "imei" => $imei, "UUID" => $requestUUID]));
            $this->logger->warning(json_encode(["response" => $response, "imei" => $imei, "UUID" => $requestUUID]));
        }
        else{
            if($requestFeedback){
                // $writeResponse = $connection->write("0200>>>");
                $writeResponse = $this->socketSeparatedWrite($imei, "0200>>>", $connection);
                $this->logger->warning(json_encode(["writeResponse" => $writeResponse, "imei" => $imei, "UUID" => $requestUUID]));
                $this->logger->warning(json_encode(["response" => "0200>>>", "imei" => $imei, "UUID" => $requestUUID]));
            }else{
                // $writeResponse = $connection->write("00>>>");
                $writeResponse = $this->socketSeparatedWrite($imei, "00>>>", $connection);
                $this->logger->warning(json_encode(["writeResponse" => $writeResponse, "imei" => $imei, "UUID" => $requestUUID]));
                $this->logger->warning(json_encode(["response" => "00>>>", "imei" => $imei, "UUID" => $requestUUID]));
            }
        }

        

        $groupedMessages = [];
        $currentTimestamp = null;
        $skipGroup = false;
        $noTimestamp = false;

        foreach ($messages as $message) {
            if ($message['type'] === 'timestamp') {
                $currentTimestamp = $message['timestamp'];

                // Check for duplicate timestamp
                if ($this->socketManager->isDuplicateLoggerMessageTimestamp($imei, $currentTimestamp)) {
                    $skipGroup = true; // Start skipping this group
                    continue;
                }

                $skipGroup = false; // It's a valid timestamp, don't skip

                if (!isset($groupedMessages[$currentTimestamp])) {
                    $groupedMessages[$currentTimestamp] = [];
                }

                $groupedMessages[$currentTimestamp][] = $message;
            } else {
                if ($skipGroup) {
                    continue; // Skip this sub-message because it's part of a duplicate timestamp group
                }

                if ($currentTimestamp !== null) {
                    $groupedMessages[$currentTimestamp][] = $message;
                } else {
                    $noTimestamp = true;
                }
            }
        }

        $flattenedMessages = [];

        foreach ($groupedMessages as $timestampGroup) {
            foreach ($timestampGroup as $message) {
                $flattenedMessages[] = $message;
            }
        }


        if($noTimestamp){
            $separatedMessages = $this->separateMessages($messages, $saldoOk);
        }else{
            $separatedMessages = $this->separateMessages($flattenedMessages, $saldoOk);
        }

        

        if (array_search('on', array_column(array_column($separatedMessages, 0), 'type')) !== false) {
            $separatedMessages = array_filter($separatedMessages, fn($item) => strpos($item[0]['type'], 'pay_') !== 0 && $item[0]['type'] !== 'token_drop');
        }

        $this->logger->warning(json_encode(["processedMessages" => json_encode($separatedMessages), "imei" => $imei, "UUID" => $requestUUID]));

        foreach ($separatedMessages as $messages){
            if($messages and ($messages[0]['type'] === "feedback" or $messages[0]['type'] === 'timestamp')){
                continue;
            }

            if ($this->params->get('appEnv') == 'dev'){
                // Dont store or send to SQS while dev
                // return $imei;
                continue;
            }


            // REMOVE WHEN PUSHING TO PROD!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
            return $imei;

            $message = $this->messageService->create($messages, $imei);
            $this->bus->dispatch(new LoggerMessage($message["imei"], $message["data"], $message["id"], $message["created_at"]));
        }
        

        return $imei;
    }

    private function isValidData(string $data, string $imei): bool
    {
        return isset($data[16]) and $data[16] === "#" and ctype_digit($imei);
    }

    private function separateMessages($data, $saldoOk) {
        $separatedMsgs = [];

        foreach ($data as $message) {
            $msgType = $message['type'];

            if (!isset($separatedMsgs[$msgType])) {
                $separatedMsgs[$msgType] = [];
            }

            if ($msgType != 'pay_rfcard' || $saldoOk) {
                $separatedMsgs[$msgType][] = $message;
            }
        }

        $msgsArray = [];
        foreach ($separatedMsgs as $msgGroup) {
            $msgsArray[] = $msgGroup;
        }

        return $msgsArray;
    }

    private function decodeData(string $data, string $salt): ?string
    {
        if ($data[0] === '#') return $data;
        if ($data[0] === '$') {
            $encoded = hex2bin(substr($data, 1));
            $decryptedData = $this->decryptionService->xxteaDecrypt($encoded, $salt);
            if($decryptedData[0] === "#"){
                return $decryptedData;
            }
            return "#" . $decryptedData;
        }
        return null;
    }

    private function extractMessages(string $data, $config): array
    {
        $numMessages = ord($data[0]) - ord('0');
        $messages = [];

        $data = substr($data, 1);

        for ($i = 0; $i < $numMessages; $i++) {
            [$msg, $data] = $this->decodeMessage($data, $config);
            if ($msg === null) continue;
            $messages[] = $msg;
        }

        return $messages;
    }

    private function decodeMessage(string $data, $config): array
    {
        $type = $data[1] ?? '0';
        $serviceMode = $data[2] ?? '';

        switch ($type) {
            case 'O': return [['type' => 'on'], substr($data, 3)];
            case 'F': return [['type' => 'feedback'], substr($data, 3)];
            case 'P': return [
                ['type' => 'ping', 'version' => substr($data, 3, 3), 'rssi' => ord($data[6]) - ord('0')],
                substr($data, 7)
            ];
            case 'T':
                $timestampHex = substr($data, 2, 8);
                $timestamp = hexdec($timestampHex);

                return [[
                    'type' => 'timestamp',
                    'timestamp' => $timestamp
                ], substr($data, 10)];

            case 'E': return [['type' => 'empty'], substr($data, 3)];
            case 'C':
                $acceptor = ($data[3] === '0') ? 'pulse' : 'parallel';
                $line = hexdec(substr($data, 4, 4));
                $paymentType = "pay_coin";

                if ($acceptor === 'pulse' && isset($config['pulse']) && isset($config['pulse'][$line])) {
                    $lineConfig = $config['pulse'][$line];
                    if (isset($lineConfig['type'])) {
                        if ($lineConfig['type'] === 'token') {
                            $paymentType = 'pay_token';
                        } elseif ($lineConfig['type'] === 'card') {
                            $paymentType = 'pay_card';
                        } elseif ($lineConfig['type'] === 'zeton') {
                            $paymentType = 'token_drop';
                        }
                    }
                }

                if ($acceptor === 'parallel' && isset($config['parallel']) && isset($config['parallel']['token_values'])) {
                    if(isset($config['parallel']['token_values'][$line])){
                        $paymentType = 'token_drop';
                    }
                }

                return [
                    [
                        'type' => $paymentType,
                        'service_mode' => $serviceMode === '1',
                        'acceptor' => $acceptor,
                        'line' => $line
                    ],
                    substr($data, 8)
                ];
            case 'S':
                $len = ord($data[0]) - ord('0');
                $status = ($data[3] === '1');
                $transactionId = substr($data, 4, $len - 3);

                return [[
                    'type' => 'pay_sms',
                    'service_mode' => $serviceMode === '1',
                    'status' => $status,
                    'transaction_id' => $transactionId
                ], substr($data, $len + 1)];
            case 'c':
                $len = ord($data[0]) - ord('0');
                $transactionId = substr($data, 3, $len - 2);
                $filteredPayload = $this->decryptionService->revertFilter(str_split($transactionId), $config);

                if(isset($config["cctalk"]["token_values"])){
                    foreach($config["cctalk"]["token_values"] as $key => $value){
                        if($filteredPayload == $key){
                            return [[
                                'type' => 'token_drop',
                                'service_mode' => $serviceMode === '1',
                                'payload' => $filteredPayload
                            ], substr($data, $len + 1)];
                        }
                    }
                }

                return [[
                    'type' => 'pay_cctalk',
                    'service_mode' => $serviceMode === '1',
                    'payload' => $filteredPayload
                ], substr($data, $len + 1)];
            case 'm':
                $len = ord($data[0]) - ord('0');
                $transactionId = substr($data, 3, $len - 2);
                $filteredPayload = $this->decryptionService->revertFilter(str_split($transactionId), $config);

                return [[
                    'type' => 'pay_mdb',
                    'service_mode' => $serviceMode === '1',
                    'payload' => $filteredPayload
                ], substr($data, $len + 1)];
            case 'e':
                $len = ord($data[0]) - ord('0');
                $transactionId = substr($data, 3, $len - 2);
                $filteredPayload = $this->decryptionService->revertFilter(str_split($transactionId), $config);

                return [[
                    'type' => 'pay_exec',
                    'service_mode' => $serviceMode === '1',
                    'payload' => $filteredPayload
                ], substr($data, $len + 1)];
            case 's':
                $len = ord($data[0]) - ord('0');
                $transactionId = substr($data, 3, $len - 2);
                $filteredPayload = $this->decryptionService->revertFilter(str_split($transactionId), $config);

                return [[
                    'type' => 'pay_sci',
                    'service_mode' => $serviceMode === '1',
                    'payload' => $filteredPayload
                ], substr($data, $len + 1)];
            case 'R':
                $len = ord($data[0]) - ord('0');
                $status = ($data[3] === '1');
                $cardId = substr($data, 4, $len - 3);

                return [[
                    'type' => 'pay_rfcard',
                    'service_mode' => $serviceMode === '1',
                    'status' => $status,
                    'card_id' => $cardId
                ], substr($data, $len + 1)];
            default:
                return [null, $data];
        }
    }

    private function handleServerMessages($serverMessage, $connection){
        if($serverMessage->type == SocketCommunication::DirectWrite->value){
            $socket = $this->socketManager->getSocket($serverMessage->imei);
            if($socket){
                $socket->write($serverMessage->data);
                $connection->write(json_encode(["message" => "Success"]));
            }else{
                $connection->write(json_encode(["error" => "Logger not connected"]));
            }
        }
        if($serverMessage->type ==  SocketCommunication::RequestImeiStatus->value){
            $loggerData = $this->socketManager->getConnectedLogger($serverMessage->imei);
            if($loggerData){
                $connection->write(json_encode($loggerData));
            }else{
                $connection->write(json_encode(["error" => "Device with that IMEI ({$serverMessage->imei}) is not connected."]));
            }
        }
        if($serverMessage->type == SocketCommunication::ReloadFirmware->value){
            $this->firmwareService->loadFirmware();
        }
        if($serverMessage->type == SocketCommunication::UpdateDeviceFirmware->value){
            $this->socketManager->addLoggerToUpdate($serverMessage->imei, true);
        }
    }

    private function generateUUID() {
        return sprintf('%04x%04x-%04x-%04x-%04x-%04x%04x%04x',
            mt_rand(0, 0xffff), mt_rand(0, 0xffff),
            mt_rand(0, 0xffff),
            mt_rand(0, 0x0fff) | 0x4000,
            mt_rand(0, 0x3fff) | 0x8000,
            mt_rand(0, 0xffff), mt_rand(0, 0xffff), mt_rand(0, 0xffff)
        );
    }

   private function socketSeparatedWrite($imei, $data, $connection){
    if (!$connection) {
        return null;
    }

    while (true) {
        $lastMessage = $this->socketManager->getSocketLastMessage($imei);
        $now = microtime(true) * 1000; // current time in ms

        $delta = ($lastMessage !== null) ? ($now - $lastMessage) : 1000;

        if ($delta >= 100) {
            $result = $connection->write($data);
            $this->socketManager->addSocketLastMessage($imei, $now);
            return $result;
        } else {
            $delay = 100 - $delta;
            if ($delay <= 0) {
                $delay = 1;
            }
            usleep($delay * 1000); // sleep for $delay ms
            }
        }
    }

    private function sendKeepAlivePackets(){
        $sockets = $this->socketManager->getAllSockets();
        $this->logger->warning("Keepalive sent");

        foreach($sockets as $imei => $socket){
            try{
                // $socket->write("00>>>");
                $this->socketSeparatedWrite($imei, "00>>>", $socket);
            }catch(\Exception $e){
                $this->logger->warning(json_encode(["error" => "Error sending a KeepAlive packet", "imei" => $imei]));
            }
        }
    }
}
