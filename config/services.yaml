# This file is the entry point to configure your own services.
# Files in the packages/ subdirectory configure your dependencies.

# Put parameters here that don't need to change on each machine where the app is deployed
# https://symfony.com/doc/current/best_practices.html#use-parameters-for-application-configuration
parameters:
    ecs_metadata_url: '%env(resolve:ECS_CONTAINER_METADATA_URI_V4)%'
    coreApiEndpoint: '%env(resolve:CORE_API_ENDPOINT)%'
    billsApiEndpoint: '%env(resolve:BILLS_API_ENDPOINT)%'
    loyaltyApiEndpoint: '%env(resolve:LOYALTY_API_ENDPOINT)%'
    poreznaUrl: '%env(resolve:POREZNA_URL)%'
    appEnv: '%env(resolve:APP_ENV)%'
    cwAccessKey: '%env(resolve:CW_ACCESS_KEY)%'
    cwSecret: '%env(resolve:CW_SECRET)%'
    noxyTcpEndpoint: '%env(resolve:NOXY_TCP_ENDPOINT)%'

services:

    cloudwatch_client:
      class: Aws\CloudWatchLogs\CloudWatchLogsClient
      arguments:
          -
              credentials: { key: "%cwAccessKey%", secret: "%cwSecret%" }
              region: "eu-central-1"
              version: "latest"

    cloudwatch_handler:
        class: PhpNexus\Cwh\Handler\CloudWatch
        arguments:
            - "@cloudwatch_client"
            - "noxy-logs-dev"              # groupName
            - "noxy-stream" # streamName
            - 30                     # retentionDays
            - 0                  # logsInBatch
            - { mytag: "tag" }       # tags
            - WARNING                # logLevel

    # default configuration for services in *this* file
    _defaults:
        autowire: true      # Automatically injects dependencies in your services.
        autoconfigure: true # Automatically registers your services as commands, event subscribers, etc.

    Twig\Extra\Inky\InkyExtension:
        tags: ['twig.extension']

    Twig\Extra\CssInliner\CssInlinerExtension:
        tags: ['twig.extension']

    # makes classes in src/ available to be used as services
    # this creates a service per class whose id is the fully-qualified class name
    App\Serializer\NayaxMessageSerializer:
        tags: ['messenger.transport.serializer']
    App\:
        resource: '../src/'
        exclude:
            - '../src/DependencyInjection/'
            - '../src/Entity/'
            - '../src/Kernel.php'

    # add more service definitions when explicit configuration is needed
    # please note that last definitions always *replace* previous ones
